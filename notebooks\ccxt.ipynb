import asyncio
import ccxt.pro as ccxtpro
import ccxt

async def watch_ticker(symbol):
    exchange = ccxtpro.mexc()

    # 測試同步 API 確保網絡連線正常
    try:
        ticker = ccxt.mexc().fetch_ticker(symbol)
        print("初始測試 ticker:", ticker)
    except ccxt.NetworkError as e:
        print("網絡錯誤:", e)
        return
    except ccxt.ExchangeError as e:
        print("交易所錯誤:", e)
        return
    except Exception as e:
        print("其他錯誤:", e)
        return

    # WebSocket 監聽
    try:
        while True:
            ticker = await exchange.watch_ticker(symbol)
            print("WebSocket ticker:", ticker)
    except ccxt.NetworkError as e:
        print("WebSocket 網絡錯誤:", e)
    except ccxt.ExchangeError as e:
        print("WebSocket 交易所錯誤:", e)
    except Exception as e:
        print("WebSocket 其他錯誤:", e)
    finally:
        await exchange.close()

await watch_ticker("BTC_USDT")

