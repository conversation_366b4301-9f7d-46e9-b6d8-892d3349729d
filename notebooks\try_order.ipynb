{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"..\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import dotenv\n", "dotenv.load_dotenv()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "API_KEY = str(os.getenv(\"API_KEY\"))\n", "API_SECRET = str(os.getenv(\"API_SECRET\"))\n", "COOKIE = str(os.getenv(\"COOKIE\"))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from src.api import MEXC, OrderSide"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["client = MEXC(API_KEY, API_SECRET, COOKIE, testnet=True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["df = client.get_candles()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(117200.1)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["current_close = df.iloc[-1][\"close\"]\n", "current_close"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(3.0)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["size = round((40 / current_close)/0.0001, 0)\n", "size"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'success': True,\n", " 'code': 0,\n", " 'data': {'orderId': '701267886710276608', 'ts': 1752865693125}}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["client.create_order(\n", "    symbol=\"BTC_USDT\",\n", "    side=OrderSide.OPEN_LONG,\n", "    size_in_contract=size,\n", "    leverage=1,\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'success': True,\n", " 'code': 0,\n", " 'data': {'orderId': '701267973674975744', 'ts': 1752865713858}}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["client.create_order(\n", "    symbol=\"BTC_USDT\",\n", "    side=OrderSide.CLOSE_LONG,\n", "    size_in_contract=size,\n", "    leverage=1,\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "arbitrage-trading", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}